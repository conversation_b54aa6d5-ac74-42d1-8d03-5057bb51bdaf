// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ChangeEmailScreen extends StatefulWidget {
  const ChangeEmailScreen({super.key});

  @override
  State<ChangeEmailScreen> createState() => _ChangeEmailScreenState();
}

class _ChangeEmailScreenState extends State<ChangeEmailScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: NewCustomAppbar(
        showHeaderTitle: true,
        headerText: 'Privacy and Security',
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
          vertical: Sizer.height(20),
        ),
        children: [
          Text(
            "Manage all your privacy and security settings below.",
            style: AppTypography.text16.copyWith(
              color: AppColors.gray93,
            ),
          ),
        ],
      ),
    );
  }
}
