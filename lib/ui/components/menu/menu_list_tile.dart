import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';

class MenuListTile extends StatelessWidget {
  const MenuListTile({
    super.key,
    this.iconData,
    required this.title,
    this.trailingWidget,
    this.useImageIcon = false,
    this.isSvg = false,
    this.showTrailing = true,
    this.imageIcon = AppImages.iconLogo,
    this.bgColor,
    this.iconColor,
    this.onPressed,
  });

  final IconData? iconData;
  final String title;
  final Widget? trailingWidget;
  final bool useImageIcon;
  final bool isSvg;
  final bool showTrailing;
  final String imageIcon;
  final Color? bgColor;
  final Color? iconColor;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(Sizer.radius(8)),
            decoration: BoxDecoration(
              color: bgColor ?? AppColors.blue100,
              borderRadius: BorderRadius.circular(Sizer.radius(4)),
            ),
            child: useImageIcon
                ? isSvg
                    ? svgHelper(imageIcon, height: Sizer.height(24))
                    : imageHelper(imageIcon, height: Sizer.height(24))
                : iconData != null
                    ? Icon(
                        iconData,
                        size: Sizer.radius(24),
                        color: iconColor ?? AppColors.lightBlue,
                      )
                    : const SizedBox.shrink(),
          ),
          const XBox(8),
          Text(title,
              style: AppTypography.text16.copyWith(
                color: iconColor ?? AppColors.iconBlack800,
              )),
          if (showTrailing) const Spacer(),
          if (showTrailing)
            Container(
              child: trailingWidget ??
                  Icon(
                    Iconsax.arrow_right_3,
                    size: Sizer.radius(24),
                    color: AppColors.iconBlack800,
                  ),
            ),
        ],
      ),
    );
  }
}
