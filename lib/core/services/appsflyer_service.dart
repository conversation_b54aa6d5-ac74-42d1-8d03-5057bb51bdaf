import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:korrency/core/core.dart';

class AppsflyService {
  static late AppsflyerSdk appsflyerSdk;

  static init() async {
    AppsFlyerOptions appsFlyerOptions = AppsFlyerOptions(
      afDevKey: dotenv.env["AF_DEV_KEY"]!,
      appId: dotenv.env["AF_APP_ID"]!,
      showDebug: true,
      timeToWaitForATTUserAuthorization: 15, // for iOS 14.5
      //appInviteOneLink: oneLinkID, // Optional field
      disableAdvertisingIdentifier: false, // Optional field
      disableCollectASA: false,
    ); // Optional field

    appsflyerSdk = AppsflyerSdk(appsFlyerOptions);

    appsflyerSdk.onInstallConversionData((res) {
      printty("onInstallConversionData: $res");
    });

    appsflyerSdk.onAppOpenAttribution((res) {
      printty("onAppOpenAttribution: $res");
    });

    appsflyerSdk.onDeepLinking((DeepLinkResult dp) {
      switch (dp.status) {
        case Status.FOUND:
          printty("link path: 00 ==> : $dp");
          printty("link path: 01 ==> : ${dp.deepLink?.clickEvent}");
          printty("link path: link ==> : ${dp.deepLink?.clickEvent["link"]}");
          try {
            if (dp.deepLink?.clickEvent != null &&
                dp.deepLink?.clickEvent["link"] != null &&
                NavigatorKeys.appNavigatorKey.currentContext != null) {
              NavigatorKeys.appNavigatorKey.currentContext!
                  .read<OnBoardVM>()
                  .extractDeepLinkValue(dp.deepLink?.clickEvent["link"]);

              printty(
                  "AppsFlyerService: deep link path initialized.....${dp.deepLink?.clickEvent["link"]}");
              // PageSwitcherService(navigatorKey.currentContext!)
              //     .switchPage(deepLink: vm.deepLinkPath);
            }
          } catch (e) {
            printty(e.toString());
          }
          break;
        case Status.NOT_FOUND:
          printty("deep link not found");
          break;
        case Status.ERROR:
          printty("deep link error: ${dp.error}");
          break;
        case Status.PARSE_ERROR:
          printty("deep link status parsing error");
          break;
      }
    });

    appsflyerSdk.initSdk(
        registerConversionDataCallback: true,
        registerOnAppOpenAttributionCallback: true,
        registerOnDeepLinkingCallback: true);

    await logEvent("appInit", {"App": "App Open"});

    // printty(s.toString());
    printty("===> appflyer initiated...");
  }

  static Future<bool?> logEvent(String eventName, Map? eventValues) async {
    bool? result = false;
    printty(appsflyerSdk.toString());
    printty("-==-=-=-=121");
    try {
      result = await appsflyerSdk.logEvent(eventName, eventValues);
      printty(result.toString());
      printty("app flyer ok=====>");
      return result;
    } on Exception catch (_) {
      return result;
    }
  }
}
